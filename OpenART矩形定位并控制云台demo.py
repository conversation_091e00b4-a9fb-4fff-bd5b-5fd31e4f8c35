import sensor, image, time
from machine import UART

# 背景除了黑色矩形外尽量为白色

uart = UART(2, baudrate=460800)         # 初始化串口 波特率设置为460800

sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.set_framerate(50)
sensor.skip_frames(time = 2000)
clock = time.clock()

goal_bias_x = 0
goal_bias_y = 0
bias_count_x = 0
last_goal_bias_x = 0

while(True):
    clock.tick()
    img = sensor.snapshot()

    for r in img.find_blobs([(0, 31, -128, 127, -128, 6)], pixels_threshold=200, area_threshold=200):

        # 判断是否满足矩形条件
        if r.w()/r.h() < 1.6 and r.w()/r.h() > 1.20:
            img.draw_rectangle(r.rect(), color = (255, 0, 0), thickness = 5)
            img.draw_cross(int(r.x() + r.w()/2)-2, (int)(r.y() + r.h()/2)-2, color = (0, 255, 0), size = 10, thickness = 5)
            goal_bias_x = int(r.x() + r.w() / 2 - img.width() / 2)
            goal_bias_y = int(r.y() + r.h() / 2 - img.height() / 2)

            # 计算输出
            pid_out = goal_bias_x * abs(goal_bias_x) * 0.03 + goal_bias_x * 0.1 + (goal_bias_x - last_goal_bias_x) * 10
            last_goal_bias_x = goal_bias_x

            # 计算发送给云台的定位数据
            bias_count_x = bias_count_x - int(pid_out)
            # 输出限幅
            if bias_count_x > 10000: bias_count_x = 10000
            if bias_count_x < -10000: bias_count_x = -10000

    #print(f"SET-LOCATION,{bias_count_x},{0}\r\n")
    uart.write(f"SET-LOCATION,{bias_count_x},{0}\r\n")
    print("FPS %f" % clock.fps())
